1.com.howbuy.tms.high.batch.service.business.fundfilemonitor.FundFileMessageProcessor  这个是处理清算的文件通知消息类,ASS0009是05文件,文件都是全量数据,每次都是删除之前的io记录,写入新的io数据
2.你参考com.howbuy.tms.high.batch.service.business.fundfilemonitor.FundVolCheckFileProcessor,写出05文件,落的io表,表实体你自己在dao层加上
3.io表落库了,你需要参考项目中事件发送处理机制,发送05文件io完成事件消息
4.io完成事件消息处理:将io表数据写入正式05文件记录表,写入之前需要校验下两个文件的数据差异,就校验io表数据条数跟正式表的数据条数,如果差异超过10%,告警,不写入正式表,告警逻辑用 OpsSysMonitor.warn(msg, OpsSysMonitor.ERROR);具体你参考项目中AbstractFileImportService的应用
5.05文件正式表,表实体你自己按照rules加在dao上

